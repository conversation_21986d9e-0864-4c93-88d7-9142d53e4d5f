import torch
import torch.nn as nn
import transformers

# 导入模块化组件
from .predictors import PredictorRegistry
from .activation_stats import ActivationStatsCollector
from .sensitivity_analyzer import SensitivityAnalyzer, DynamicSparsityAllocator

# 禁用TF32以确保数值精度
torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cudnn.allow_tf32 = False


class Giraffe:
    """
    Giraffe
    
    核心思想：
    1. 使用预测器根据层输入自动选择最不重要的权重
    2. 收集激活统计信息作为预测器的输入特征
    3. 预测器可以是简单的启发式规则或复杂的机器学习模型
    4. 支持动态调整稀疏策略，适应不同的输入模式
    
    与其他方法的区别：
    - 相比Wanda：不仅考虑激活幅度，还考虑更丰富的上下文信息
    - 相比SparseGPT：避免复杂的Hessian计算，使用预测器简化决策过程
    - 核心优势：自适应性强，可以根据实际输入调整剪枝策略
    """

    def __init__(self, layer, predictor=None, layer_id=0, layer_name="none"):
        """
        初始化Giraffe稀疏器
        
        Args:
            layer: 要剪枝的神经网络层
            predictor: 预测器函数，接收激活统计并返回重要性分数
            layer_id: 层ID
            layer_name: 层名称
        """
        self.layer = layer
        self.dev = self.layer.weight.device
        self.predictor = predictor or PredictorRegistry.get_predictor("dolphin")
        
        W = layer.weight.data.clone()
        
        # 处理不同类型的层
        if isinstance(self.layer, nn.Conv2d):
            W = W.flatten(1)
        if isinstance(self.layer, transformers.Conv1D):
            W = W.t()
            
        self.rows = W.shape[0]     # 输出维度
        self.columns = W.shape[1]  # 输入维度
        self.nsamples = 0

        # 使用模块化的激活统计收集器
        self.stats_collector = ActivationStatsCollector(self.columns, self.dev)
        
        self.layer_id = layer_id
        self.layer_name = layer_name

    def add_batch(self, inp, out):
        """
        添加一个批次的激活数据来更新统计信息
        
        Args:
            inp: 输入激活张量
            out: 输出激活张量（未使用）
        """
        if len(inp.shape) == 2:
            inp = inp.unsqueeze(0)
        tmp = inp.shape[0]
        
        # 处理线性层：reshape并转置
        if isinstance(self.layer, nn.Linear) or isinstance(self.layer, transformers.Conv1D):
            if len(inp.shape) == 3:
                inp = inp.reshape((-1, inp.shape[-1]))
            inp = inp.t()  # 转置为 [features, samples]

        # 使用统计收集器处理激活数据
        self.stats_collector.add_batch(inp, self.layer_name)
        
        self.nsamples += tmp
        
        # 添加统计信息验证
        if self.nsamples % 10 == 0:  # 每10个批次检查一次
            self.stats_collector.validate_stats(self.layer_name)

    def prune(self, sparsity, predictor=None):
        """
        执行基于预测器的剪枝
        
        核心流程：
        1. 调用预测器计算每个权重的重要性分数
        2. 根据稀疏度阈值选择最不重要的权重进行剪枝
        3. 直接将选中的权重置零（简单有效的剪枝策略）
        
        Args:
            sparsity: 目标稀疏度（0-1之间）
            predictor: 可选的预测器，覆盖默认预测器
        """
        # 使用指定的预测器或默认预测器
        current_predictor = predictor or self.predictor
        
        W = self.layer.weight.data.clone()
        if isinstance(self.layer, nn.Conv2d):
            W = W.flatten(1)
        if isinstance(self.layer, transformers.Conv1D):
            W = W.t()
        W = W.float()

        # 准备层信息
        layer_info = {
            'layer_id': self.layer_id,
            'layer_name': self.layer_name,
            'shape': W.shape,
            'nsamples': self.nsamples
        }

        # 获取激活统计信息
        activation_stats = self.stats_collector.get_stats()

        # 调用预测器计算重要性分数
        importance_scores = current_predictor(activation_stats, W, layer_info)
        
        # 确保重要性分数的维度正确（应该与权重矩阵同形状）
        if importance_scores.dim() == 1:
            # 如果预测器返回一维向量，扩展为权重矩阵形状
            importance_scores = importance_scores.unsqueeze(0).expand(W.shape[0], -1)
        elif importance_scores.shape != W.shape:
            # 如果维度不匹配，进行适当的调整
            if importance_scores.numel() == W.numel():
                importance_scores = importance_scores.reshape(W.shape)
            else:
                raise ValueError(f"重要性分数维度 {importance_scores.shape} 与权重维度 {W.shape} 不匹配")
        
        # 根据稀疏度确定阈值 - 使用分位数方法，处理大张量情况
        flat_scores = importance_scores.flatten()
        
        # 对于过大的张量，使用采样方法来计算分位数
        if flat_scores.numel() > 100_000_000:  # 如果元素数量超过1亿
            # 随机采样一部分来估计分位数
            sample_size = min(1_000_000, flat_scores.numel() // 100)  # 采样1%但不超过100万
            indices = torch.randperm(flat_scores.numel(), device=flat_scores.device)[:sample_size]
            sampled_scores = flat_scores[indices]
            thresh = torch.quantile(sampled_scores, sparsity)
        else:
            # 对于较小的张量，直接计算分位数
            try:
                thresh = torch.quantile(flat_scores, sparsity)
            except RuntimeError as e:
                if "too large" in str(e):
                    # 如果仍然太大，使用排序方法
                    sorted_scores, _ = torch.sort(flat_scores)
                    index = int(sparsity * len(sorted_scores))
                    thresh = sorted_scores[index]
                else:
                    raise e
        
        # 创建剪枝掩码：重要性分数低于阈值的权重被剪枝
        prune_mask = importance_scores <= thresh
        
        # 应用剪枝：将选中的权重置零
        W[prune_mask] = 0
        
        # 恢复原始权重形状并更新层权重
        if isinstance(self.layer, transformers.Conv1D):
            W = W.t()
        self.layer.weight.data = W.reshape(self.layer.weight.shape).to(self.layer.weight.data.dtype)
        
        # 打印剪枝信息 - 修复层名显示问题
        total_params = W.numel()
        pruned_params = prune_mask.sum().item()
        actual_sparsity = pruned_params / total_params
        
        # 确保层名正确显示
        display_name = self.layer_name if self.layer_name != "none" else f"Layer{self.layer_id}"
        print(f"{display_name}: pruned {pruned_params}/{total_params} "
              f"parameters ({actual_sparsity:.2%} sparsity, target: {sparsity:.2%})")
        
        # 如果实际稀疏度与目标相差太大，给出警告
        if abs(actual_sparsity - sparsity) > 0.05:  # 5%的容忍度
            print(f"  警告: 实际稀疏度 {actual_sparsity:.2%} 与目标稀疏度 {sparsity:.2%} 相差较大")
        
        return actual_sparsity

    def set_predictor(self, predictor):
        """设置新的预测器"""
        self.predictor = predictor

    def get_activation_stats(self):
        """获取当前的激活统计信息"""
        return self.stats_collector.get_stats()

    def set_predictor_strategy(self, strategy="dolphin"):
        """
        设置预测器策略
        
        Args:
            strategy: 预测器策略名称
                - "dolphin": 增强预测器
                - "wanda": Wanda预测器
                - "magnitude": 权重幅度预测器
                - "snip": SNIP预测器
        """
        try:
            self.predictor = PredictorRegistry.get_predictor(strategy)
            print(f"预测器策略设置为: {strategy}")
        except ValueError as e:
            print(str(e))
            raise

    @staticmethod
    def dynamic_sparsity(giraffe_layers, target_sparsity, layer_id=0):
        """
        动态稀疏度分配算法
        
        Args:
            giraffe_layers: {layer_name: Giraffe对象} 的字典
            target_sparsity: 目标平均稀疏度
            layer_id: 层ID
            
        Returns:
            dict: {layer_name: 分配的稀疏度}
        """
        return DynamicSparsityAllocator.allocate_sparsity(giraffe_layers, target_sparsity, layer_id)

    def free(self):
        """释放内存"""
        if hasattr(self, 'stats_collector'):
            self.stats_collector = None
        torch.cuda.empty_cache()
