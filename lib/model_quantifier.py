"""
统一的大模型量化模块
整合了量化压缩技术
"""

import torch
import torch.nn as nn
import numpy as np
import json
from typing import Dict, <PERSON><PERSON>, Any
from tqdm import tqdm
import gc


class ModelQuantifier:
    """统一的模型量化器"""

    def __init__(self,
                 quantification_method: str = 'auto',
                 sparsity_threshold: float = 1e-6,
                 min_compression_ratio: float = 1.2,
                 quantization_bits: int = 8,
                 min_sparsity_for_sparse: float = 0.3):

        self.quantification_method = quantification_method
        self.sparsity_threshold = sparsity_threshold
        self.min_compression_ratio = min_compression_ratio
        self.quantization_bits = quantization_bits
        self.min_sparsity_for_sparse = min_sparsity_for_sparse
        self.quantification_stats = {}

    def quantify_layer(self, layer: nn.Linear, layer_name: str = "") -> Tuple[nn.Module, Dict[str, Any]]:
        """量化单个层 - 使用量化压缩"""
        weight = layer.weight.data
        sparsity = (weight == 0).float().mean().item()

        # 计算原始内存使用
        original_params = weight.numel() + (layer.bias.numel() if layer.bias is not None else 0)
        original_memory = weight.numel() * 4 + (layer.bias.numel() * 4 if layer.bias is not None else 0)

        # 尝试量化压缩
        try:
            quantized_layer = self._create_quantized_layer(layer)
            quantized_memory = quantized_layer.get_memory_usage()
            if layer.bias is not None:
                quantized_memory += layer.bias.numel() * 4

            quantization_ratio = original_memory / quantized_memory if quantized_memory > 0 else 1.0

            stats = {
                'compressed': True,
                'original_params': original_params,
                'compressed_params': quantized_layer.get_param_count(),
                'original_memory_bytes': original_memory,
                'compressed_memory_bytes': quantized_memory,
                'compression_ratio': quantization_ratio,
                'method': 'int8_quantization',
                'sparsity': sparsity,
                'precision_loss': 0.01,
                'memory_savings': 1 - 1/quantization_ratio
            }
            return quantized_layer, stats

        except Exception as e:
            # 保持原始层
            stats = {
                'compressed': False,
                'original_params': original_params,
                'compressed_params': original_params,
                'original_memory_bytes': original_memory,
                'compressed_memory_bytes': original_memory,
                'compression_ratio': 1.0,
                'method': 'no_quantification',
                'reason': f'quantization_failed: {e}',
                'sparsity': sparsity
            }
            return layer, stats

    def _create_quantized_layer(self, layer: nn.Linear) -> 'QuantizedLinear':
        """创建量化线性层"""
        return QuantizedLinear(layer)

    def quantify_model(self, model: nn.Module) -> Tuple[nn.Module, Dict[str, Any]]:
        """量化整个模型"""
        print("开始模型量化...")

        total_original_params = 0
        total_quantified_params = 0  # 有效参数数量
        total_storage_params = 0     # 实际存储的参数数量
        total_original_memory = 0
        total_quantified_memory = 0
        quantified_layers = 0
        skipped_layers = 0
        error_layers = 0

        method_counts = {}

        # 智能检测模型层结构
        layers = None
        if hasattr(model, 'base_model') and hasattr(model.base_model, 'model') and hasattr(model.base_model.model, 'model') and hasattr(model.base_model.model.model, 'layers'):
            # PEFT模型结构
            layers = model.base_model.model.model.layers
        elif hasattr(model, 'model') and hasattr(model.model, 'layers'):
            # 合并后的模型结构
            layers = model.model.layers
        elif hasattr(model, 'layers'):
            # 直接的模型结构
            layers = model.layers
        else:
            raise AttributeError("无法找到模型的layers属性，请检查模型结构")

        print(f"找到 {len(layers)} 层需要量化")
        # 逐层量化
        for i, layer in enumerate(tqdm(layers, desc="量化模型层")):
            layer_stats = {}

            # 量化注意力层
            if hasattr(layer, 'self_attn'):
                attention = layer.self_attn
                for proj_name in ['q_proj', 'k_proj', 'v_proj', 'o_proj']:
                    if hasattr(attention, proj_name):
                        proj_layer = getattr(attention, proj_name)
                        if isinstance(proj_layer, nn.Linear):
                            quantified_layer, stats = self.quantify_layer(
                                proj_layer, f"layer_{i}.attention.{proj_name}"
                            )

                            # 替换原层
                            setattr(attention, proj_name, quantified_layer)

                            layer_stats[f'attention.{proj_name}'] = stats
                            total_original_params += stats.get('original_params', 0)
                            total_quantified_params += stats.get('compressed_params', 0)
                            # 计算实际存储的参数数量
                            if hasattr(quantified_layer, 'get_original_param_count'):
                                total_storage_params += quantified_layer.get_original_param_count()
                            else:
                                total_storage_params += stats.get('compressed_params', 0)
                            total_original_memory += stats.get('original_memory_bytes', 0)
                            total_quantified_memory += stats.get('compressed_memory_bytes', 0)

                            if stats.get('compressed', False):
                                quantified_layers += 1
                                method = stats.get('quantification_method_used', 'unknown')
                                method_counts[method] = method_counts.get(method, 0) + 1
                            elif 'error' in stats:
                                error_layers += 1
                            else:
                                skipped_layers += 1

            # 量化MLP层
            if hasattr(layer, 'mlp'):
                mlp = layer.mlp
                for proj_name in ['gate_proj', 'up_proj', 'down_proj']:
                    if hasattr(mlp, proj_name):
                        proj_layer = getattr(mlp, proj_name)
                        if isinstance(proj_layer, nn.Linear):
                            quantified_layer, stats = self.quantify_layer(
                                proj_layer, f"layer_{i}.mlp.{proj_name}"
                            )

                            # 替换原层
                            setattr(mlp, proj_name, quantified_layer)

                            layer_stats[f'mlp.{proj_name}'] = stats
                            total_original_params += stats.get('original_params', 0)
                            total_quantified_params += stats.get('compressed_params', 0)
                            # 计算实际存储的参数数量
                            if hasattr(quantified_layer, 'get_original_param_count'):
                                total_storage_params += quantified_layer.get_original_param_count()
                            else:
                                total_storage_params += stats.get('compressed_params', 0)
                            total_original_memory += stats.get('original_memory_bytes', 0)
                            total_quantified_memory += stats.get('compressed_memory_bytes', 0)

                            if stats.get('compressed', False):
                                quantified_layers += 1
                                method = stats.get('quantification_method_used', 'unknown')
                                method_counts[method] = method_counts.get(method, 0) + 1
                            elif 'error' in stats:
                                error_layers += 1
                            else:
                                skipped_layers += 1

            self.quantification_stats[f'layer_{i}'] = layer_stats

            # 清理内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()

            # 清理量化层的缓存
            for _, module in layer.named_modules():
                if hasattr(module, 'clear_cache'):
                    module.clear_cache()

            # 每处理几层就进行一次深度内存清理
            if i % 5 == 0:
                self._deep_memory_cleanup()

        # 计算总体量化统计
        overall_compression_ratio = total_original_params / max(total_quantified_params, 1)
        memory_compression_ratio = total_original_memory / max(total_quantified_memory, 1)
        param_reduction = 1 - (total_quantified_params / total_original_params) if total_original_params > 0 else 0
        memory_reduction = 1 - (total_quantified_memory / total_original_memory) if total_original_memory > 0 else 0

        quantification_report = {
            'compressed_layers': quantified_layers,
            'skipped_layers': skipped_layers,
            'error_layers': error_layers,
            'total_original_params': total_original_params,
            'total_compressed_params': total_quantified_params,  # 有效参数数量
            'total_storage_params': total_storage_params,        # 实际存储参数数量
            'total_original_memory_mb': total_original_memory / 1024 / 1024,
            'total_compressed_memory_mb': total_quantified_memory / 1024 / 1024,
            'overall_compression_ratio': overall_compression_ratio,
            'memory_compression_ratio': memory_compression_ratio,
            'param_reduction': param_reduction,
            'memory_reduction': memory_reduction,
            'effective_param_reduction': 1 - (total_quantified_params / total_original_params) if total_original_params > 0 else 0,
            'storage_param_reduction': 1 - (total_storage_params / total_original_params) if total_original_params > 0 else 0,
            'quantification_method_counts': method_counts,
            'quantification_method': f'unified_quantifier_{self.quantification_method}',
            'layer_stats': self.quantification_stats
        }

        # 不进行预热，避免内存溢出
        # 权重缓存将在实际使用时按需构建
        print("量化完成，权重将在使用时按需解压缩")

        # 最终内存清理
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

        return model, quantification_report

    def _deep_memory_cleanup(self):
        """深度内存清理"""
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()


class QuantizedLinear(nn.Module):
    """INT8量化线性层"""

    def __init__(self, original_layer: nn.Linear):
        super().__init__()

        self.in_features = original_layer.in_features
        self.out_features = original_layer.out_features

        # 复制bias
        if original_layer.bias is not None:
            self.bias = nn.Parameter(original_layer.bias.data.clone())
        else:
            self.bias = None

        # 量化权重
        self._quantize_weight(original_layer.weight.data)

    def _quantize_weight(self, weight: torch.Tensor):
        """将权重量化为INT8"""
        # 计算量化参数
        weight_min = weight.min().item()
        weight_max = weight.max().item()

        # 对称量化
        scale = max(abs(weight_min), abs(weight_max)) / 127.0

        if scale == 0:
            scale = 1.0  # 避免除零

        # 量化
        quantized_weight = torch.round(weight / scale).clamp(-127, 127).to(torch.int8)

        # 存储量化参数
        self.register_buffer('quantized_weight', quantized_weight)
        self.register_buffer('weight_scale', torch.tensor(scale, dtype=torch.float32))

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 反量化权重
        weight = self.quantized_weight.to(x.dtype) * self.weight_scale.to(x.dtype)

        # 处理bias
        bias = self.bias
        if bias is not None:
            bias = bias.to(device=x.device, dtype=x.dtype)

        return torch.nn.functional.linear(x, weight, bias)

    def get_memory_usage(self) -> int:
        """计算内存使用量（字节）"""
        # 量化权重：int8
        weight_size = self.quantized_weight.numel() * 1
        # 量化参数：float32
        scale_size = 4
        # 元数据
        metadata_size = 8

        return weight_size + scale_size + metadata_size

    def get_param_count(self) -> int:
        """获取有效参数数量"""
        quantized_params = self.quantized_weight.numel()
        bias_params = self.bias.numel() if self.bias is not None else 0
        return quantized_params + bias_params

    def get_original_param_count(self) -> int:
        """获取原始参数数量（用于对比）"""
        return self.quantized_weight.numel() + (self.bias.numel() if self.bias is not None else 0)


def quantify_model(model: nn.Module,
                  compression_method: str = 'auto',
                  sparsity_threshold: float = 1e-6,
                  min_compression_ratio: float = 1.2,
                  quantization_bits: int = 8,
                  min_sparsity_for_sparse: float = 0.3) -> Tuple[nn.Module, Dict[str, Any]]:
    """
    模型量化接口 - 使用INT8量化压缩

    Args:
        model: 要量化的模型
        compression_method: 量化方法
        sparsity_threshold: 稀疏性阈值
        min_compression_ratio: 最小压缩比要求
        quantization_bits: 量化位数
        min_sparsity_for_sparse: 稀疏压缩阈值

    Returns:
        quantified_model: 量化后的模型
        quantification_report: 量化报告
    """
    quantifier = ModelQuantifier(
        quantification_method=compression_method,
        sparsity_threshold=sparsity_threshold,
        min_compression_ratio=min_compression_ratio,
        quantization_bits=quantization_bits,
        min_sparsity_for_sparse=min_sparsity_for_sparse
    )

    return quantifier.quantify_model(model)
