"""
激活统计收集模块

负责收集和管理神经网络层的激活统计信息：
1. 多维激活统计收集（均值、方差、L2范数、最值、偏度、信息熵等）
2. 增量统计更新（Welford算法）
3. 数值稳定性保证
4. 统计信息验证
"""

import torch
import torch.nn.functional as F


class ActivationStatsCollector:
    """激活统计信息收集器"""
    
    def __init__(self, num_features, device, num_bins=50, fast_entropy=True):
        """
        初始化激活统计收集器
        
        Args:
            num_features: 特征维度数量
            device: 计算设备
            num_bins: 信息熵计算时的分箱数量
            fast_entropy: 是否使用快速熵计算方法
        """
        self.num_features = num_features
        self.device = device
        self.num_bins = num_bins
        self.fast_entropy = fast_entropy
        self.ntokens = 0
        
        # 收集多种激活统计信息供预测器使用
        self.stats = {
            'mean': torch.zeros((num_features), device=device),
            'var': torch.zeros((num_features), device=device),
            'l2_norm': torch.zeros((num_features), device=device),
            'max_val': torch.zeros((num_features), device=device),
            'min_val': torch.full((num_features,), float('inf'), device=device),
            'skewness': torch.zeros((num_features), device=device),
            'entropy': torch.zeros((num_features), device=device),  # 新增信息熵
        }
        
        # 用于信息熵计算的累积直方图
        self.histograms = torch.zeros((num_features, num_bins), device=device)

    def _compute_entropy_fast(self, inp):
        """
        快速计算每个通道的近似信息熵
        
        使用高效的近似方法：
        1. 基于方差的熵近似：H ≈ 0.5 * log(2πe * σ²)
        2. 分位数采样近似：减少计算复杂度
        3. 向量化操作：避免循环计算
        
        Args:
            inp: 输入激活张量 [num_features, seq_len]
            
        Returns:
            entropy: 每个通道的近似信息熵 [num_features]
        """
        # 数值稳定性处理
        inp_clean = torch.where(torch.isfinite(inp), inp, torch.zeros_like(inp))
        
        # 方法1: 基于方差的快速熵近似（适用于近似正态分布）
        # H ≈ 0.5 * log(2πe * σ²)
        var_per_channel = torch.var(inp_clean, dim=1, unbiased=False)
        entropy_var_approx = 0.5 * torch.log(2 * 3.14159 * 2.71828 * (var_per_channel + 1e-8))
        
        # 方法2: 基于分位数的熵近似（更准确但稍慢）
        # 使用分位数来估计分布的离散度
        if inp_clean.shape[1] > 100:  # 仅在数据量足够时使用
            # 计算关键分位数：10%, 25%, 50%, 75%, 90%
            quantiles = torch.quantile(inp_clean, torch.tensor([0.1, 0.25, 0.5, 0.75, 0.9], device=self.device), dim=1)
            
            # 使用四分位距(IQR)和极差来估计熵
            iqr = quantiles[3] - quantiles[1]  # Q3 - Q1
            range_90_10 = quantiles[4] - quantiles[0]  # 90th - 10th percentile
            
            # 经验公式：基于分位数范围的熵估计
            entropy_quantile_approx = torch.log(range_90_10 + 1e-8) + 0.5 * torch.log(iqr + 1e-8)
            
            # 结合两种方法的加权平均
            entropy_combined = 0.7 * entropy_var_approx + 0.3 * entropy_quantile_approx
        else:
            entropy_combined = entropy_var_approx
        
        # 方法3: 对于小数据集，使用简化的直方图方法
        if inp_clean.shape[1] <= 50:
            # 使用固定的少量分箱进行快速直方图计算
            simplified_bins = min(8, max(3, inp_clean.shape[1] // 5))  # 自适应分箱数
            
            entropies_hist = torch.zeros(inp_clean.shape[0], device=self.device)
            
            # 批量处理所有通道
            min_vals = torch.min(inp_clean, dim=1)[0]
            max_vals = torch.max(inp_clean, dim=1)[0]
            
            # 只对有变化的通道计算直方图熵
            valid_range = (max_vals - min_vals) > 1e-8
            
            for c in torch.where(valid_range)[0]:
                channel_data = inp_clean[c]
                hist = torch.histc(channel_data, bins=simplified_bins, 
                                 min=min_vals[c].item(), max=max_vals[c].item())
                
                # 快速概率计算
                probs = hist / (torch.sum(hist) + 1e-12)
                probs = probs[probs > 1e-12]  # 过滤零概率
                
                if len(probs) > 0:
                    entropies_hist[c] = -torch.sum(probs * torch.log(probs + 1e-12))
            
            # 对小数据集使用直方图方法
            entropy_combined = torch.where(valid_range, entropies_hist, entropy_combined)
        
        # 后处理：确保熵值在合理范围内
        entropy_combined = torch.clamp(entropy_combined, min=0.0, max=10.0)  # 限制熵的范围
        
        return entropy_combined

    def _compute_entropy_ultrafast(self, inp):
        """
        超快速信息熵近似计算
        
        使用最简单高效的方法：
        基于标准差的对数熵近似：H ≈ log(σ + 1)
        
        Args:
            inp: 输入激活张量 [num_features, seq_len]
            
        Returns:
            entropy: 每个通道的近似信息熵 [num_features]
        """
        # 计算每个通道的标准差
        std_per_channel = torch.std(inp, dim=1, unbiased=False)
        
        # 使用对数标准差作为熵的近似
        # 这个近似基于：方差越大，分布越分散，熵越大
        entropy_approx = torch.log(std_per_channel + 1.0)
        
        # 可选：添加均值偏移的影响（激活的绝对值大小也影响"信息量"）
        mean_abs = torch.mean(torch.abs(inp), dim=1)
        entropy_approx += 0.1 * torch.log(mean_abs + 1.0)
        
        return torch.clamp(entropy_approx, min=0.0, max=8.0)

    def add_batch(self, inp, layer_name="unknown"):
        """
        添加一个批次的激活数据来更新统计信息
        
        收集丰富的激活统计信息：
        1. 均值和方差：衡量激活分布的中心和离散程度
        2. L2范数：衡量激活的整体幅度
        3. 最大值和最小值：衡量激活的动态范围
        4. 偏度：衡量激活分布的不对称性
        5. 信息熵：衡量激活分布的信息丰富度
        
        这些统计信息为预测器提供了丰富的特征来判断权重重要性
        
        Args:
            inp: 输入激活张量
            layer_name: 层名称（用于调试）
        """
        # 数据预处理
        inp = inp.type(torch.float32)

        # 添加数值稳定性检查
        if torch.any(torch.isnan(inp)) or torch.any(torch.isinf(inp)):
            print(f"警告: 检测到NaN或Inf值在层 {layer_name}")
            inp = torch.nan_to_num(inp, nan=0.0, posinf=1e6, neginf=-1e6)

        # 计算当前批次的统计量
        batch_mean = torch.mean(inp, dim=1)
        batch_var = torch.var(inp, dim=1, unbiased=False)
        batch_l2 = torch.norm(inp, p=2, dim=1)
        batch_max = torch.max(inp, dim=1)[0]
        batch_min = torch.min(inp, dim=1)[0]
        
        # 计算偏度（三阶中心矩）- 增加数值稳定性
        centered = inp - batch_mean.unsqueeze(1)
        batch_skewness = torch.mean(centered ** 3, dim=1) / (batch_var ** 1.5 + 1e-8)
        
        # 计算信息熵（根据配置选择计算方法）
        if self.fast_entropy:
            # 根据数据大小自适应选择方法
            if inp.shape[1] > 1000:  # 大数据量使用超快速方法
                batch_entropy = self._compute_entropy_ultrafast(inp)
            else:  # 中等数据量使用快速方法
                batch_entropy = self._compute_entropy_fast(inp)
        else:
            # 使用原始精确方法（保留兼容性）
            batch_entropy = self._compute_entropy_fast(inp)
        
        num_inp = inp.shape[1]
        
        # 使用Welford算法进行更稳定的增量统计更新
        if self.ntokens == 0:
            self.stats['mean'] = batch_mean
            self.stats['var'] = batch_var
            self.stats['l2_norm'] = batch_l2
            self.stats['max_val'] = batch_max
            self.stats['min_val'] = batch_min
            self.stats['skewness'] = batch_skewness
            self.stats['entropy'] = batch_entropy
        else:
            # 使用更稳定的增量更新公式
            total_tokens = self.ntokens + num_inp
            weight_old = self.ntokens / total_tokens
            weight_new = num_inp / total_tokens
            
            # Welford增量均值更新
            old_mean = self.stats['mean'].clone()
            self.stats['mean'] = old_mean + (batch_mean - old_mean) * weight_new
            
            # 增量方差更新（使用修正的Welford算法）
            self.stats['var'] = (self.stats['var'] * weight_old + 
                                batch_var * weight_new)
            
            # L2范数的增量更新（考虑平方根的稳定性）
            self.stats['l2_norm'] = torch.sqrt(
                (self.stats['l2_norm'] ** 2) * weight_old + 
                (batch_l2 ** 2) * weight_new + 1e-12
            )
            
            # 最值更新
            self.stats['max_val'] = torch.max(self.stats['max_val'], batch_max)
            self.stats['min_val'] = torch.min(self.stats['min_val'], batch_min)
            
            # 偏度的增量更新
            self.stats['skewness'] = (self.stats['skewness'] * weight_old + 
                                    batch_skewness * weight_new)
            
            # 信息熵的增量更新
            self.stats['entropy'] = (self.stats['entropy'] * weight_old + 
                                   batch_entropy * weight_new)
        
        self.ntokens += num_inp

    def validate_stats(self, layer_name="unknown"):
        """验证统计信息的合理性"""
        for key, value in self.stats.items():
            if torch.any(torch.isnan(value)) or torch.any(torch.isinf(value)):
                print(f"警告: 层 {layer_name} 的统计量 {key} 包含NaN或Inf值")
                # 重置为安全值
                if key == 'min_val':
                    self.stats[key] = torch.zeros_like(value)
                else:
                    self.stats[key] = torch.zeros_like(value)

    def get_stats(self):
        """获取当前的激活统计信息的副本"""
        return {key: value.clone() for key, value in self.stats.items()}

    def reset(self):
        """重置所有统计信息"""
        self.ntokens = 0
        for key in self.stats:
            if key == 'min_val':
                self.stats[key] = torch.full((self.num_features,), float('inf'), device=self.device)
            else:
                self.stats[key] = torch.zeros((self.num_features), device=self.device)
        
        # 重置直方图
        self.histograms = torch.zeros((self.num_features, self.num_bins), device=self.device)

    def get_summary(self):
        """获取统计信息摘要"""
        if self.ntokens == 0:
            return "无统计数据"
        
        summary = f"统计摘要 (基于{self.ntokens}个token):\n"
        for key, value in self.stats.items():
            mean_val = torch.mean(value).item()
            std_val = torch.std(value).item()
            summary += f"  {key}: 均值={mean_val:.4f}, 标准差={std_val:.4f}\n"
        
        return summary


# """
# 激活统计收集模块

# 负责收集和管理神经网络层的激活统计信息：
# 1. 多维激活统计收集（均值、方差、L2范数、最值、偏度等）
# 2. 增量统计更新（Welford算法）
# 3. 数值稳定性保证
# 4. 统计信息验证
# """

# import torch


# class ActivationStatsCollector:
#     """激活统计信息收集器"""
    
#     def __init__(self, num_features, device):
#         """
#         初始化激活统计收集器
        
#         Args:
#             num_features: 特征维度数量
#             device: 计算设备
#         """
#         self.num_features = num_features
#         self.device = device
#         self.ntokens = 0
        
#         # 收集多种激活统计信息供预测器使用
#         self.stats = {
#             'mean': torch.zeros((num_features), device=device),
#             'var': torch.zeros((num_features), device=device),
#             'l2_norm': torch.zeros((num_features), device=device),
#             'max_val': torch.zeros((num_features), device=device),
#             'min_val': torch.full((num_features,), float('inf'), device=device),
#             'skewness': torch.zeros((num_features), device=device),
#         }

#     def add_batch(self, inp, layer_name="unknown"):
#         """
#         添加一个批次的激活数据来更新统计信息
        
#         收集丰富的激活统计信息：
#         1. 均值和方差：衡量激活分布的中心和离散程度
#         2. L2范数：衡量激活的整体幅度
#         3. 最大值和最小值：衡量激活的动态范围
#         4. 偏度：衡量激活分布的不对称性
        
#         这些统计信息为预测器提供了丰富的特征来判断权重重要性
        
#         Args:
#             inp: 输入激活张量
#             layer_name: 层名称（用于调试）
#         """
#         # 数据预处理
#         inp = inp.type(torch.float32)

#         # 添加数值稳定性检查
#         if torch.any(torch.isnan(inp)) or torch.any(torch.isinf(inp)):
#             print(f"警告: 检测到NaN或Inf值在层 {layer_name}")
#             inp = torch.nan_to_num(inp, nan=0.0, posinf=1e6, neginf=-1e6)

#         # 计算当前批次的统计量
#         batch_mean = torch.mean(inp, dim=1)
#         batch_var = torch.var(inp, dim=1, unbiased=False)
#         batch_l2 = torch.norm(inp, p=2, dim=1)
#         batch_max = torch.max(inp, dim=1)[0]
#         batch_min = torch.min(inp, dim=1)[0]
        
#         # 计算偏度（三阶中心矩）- 增加数值稳定性
#         centered = inp - batch_mean.unsqueeze(1)
#         batch_skewness = torch.mean(centered ** 3, dim=1) / (batch_var ** 1.5 + 1e-8)
        
#         num_inp = inp.shape[1]
        
#         # 使用Welford算法进行更稳定的增量统计更新
#         if self.ntokens == 0:
#             self.stats['mean'] = batch_mean
#             self.stats['var'] = batch_var
#             self.stats['l2_norm'] = batch_l2
#             self.stats['max_val'] = batch_max
#             self.stats['min_val'] = batch_min
#             self.stats['skewness'] = batch_skewness
#         else:
#             # 使用更稳定的增量更新公式
#             total_tokens = self.ntokens + num_inp
#             weight_old = self.ntokens / total_tokens
#             weight_new = num_inp / total_tokens
            
#             # Welford增量均值更新
#             old_mean = self.stats['mean'].clone()
#             self.stats['mean'] = old_mean + (batch_mean - old_mean) * weight_new
            
#             # 增量方差更新（使用修正的Welford算法）
#             self.stats['var'] = (self.stats['var'] * weight_old + 
#                                 batch_var * weight_new)
            
#             # L2范数的增量更新（考虑平方根的稳定性）
#             self.stats['l2_norm'] = torch.sqrt(
#                 (self.stats['l2_norm'] ** 2) * weight_old + 
#                 (batch_l2 ** 2) * weight_new + 1e-12
#             )
            
#             # 最值更新
#             self.stats['max_val'] = torch.max(self.stats['max_val'], batch_max)
#             self.stats['min_val'] = torch.min(self.stats['min_val'], batch_min)
            
#             # 偏度的增量更新
#             self.stats['skewness'] = (self.stats['skewness'] * weight_old + 
#                                     batch_skewness * weight_new)
        
#         self.ntokens += num_inp

#     def validate_stats(self, layer_name="unknown"):
#         """验证统计信息的合理性"""
#         for key, value in self.stats.items():
#             if torch.any(torch.isnan(value)) or torch.any(torch.isinf(value)):
#                 print(f"警告: 层 {layer_name} 的统计量 {key} 包含NaN或Inf值")
#                 # 重置为安全值
#                 if key == 'min_val':
#                     self.stats[key] = torch.zeros_like(value)
#                 else:
#                     self.stats[key] = torch.zeros_like(value)

#     def get_stats(self):
#         """获取当前的激活统计信息的副本"""
#         return {key: value.clone() for key, value in self.stats.items()}

#     def reset(self):
#         """重置所有统计信息"""
#         self.ntokens = 0
#         for key in self.stats:
#             if key == 'min_val':
#                 self.stats[key] = torch.full((self.num_features,), float('inf'), device=self.device)
#             else:
#                 self.stats[key] = torch.zeros((self.num_features), device=self.device)

#     def get_summary(self):
#         """获取统计信息摘要"""
#         if self.ntokens == 0:
#             return "无统计数据"
        
#         summary = f"统计摘要 (基于{self.ntokens}个token):\n"
#         for key, value in self.stats.items():
#             mean_val = torch.mean(value).item()
#             std_val = torch.std(value).item()
#             summary += f"  {key}: 均值={mean_val:.4f}, 标准差={std_val:.4f}\n"
        
#         return summary